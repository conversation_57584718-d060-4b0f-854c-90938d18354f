<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GolfMap</title>
    <link
      rel="stylesheet"
      href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
      crossorigin=""
    />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />
</head>
<body>
  <div id="map" style="height: 100vh; width: 100%; margin: auto;">
    <div id="container" style="position: absolute; top: 10px; left: 10px; background-color: white; padding: 15px; border: 1px solid #ccc; z-index: 1000; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); min-width: 250px;">
      <h3 style="margin: 0 0 10px 0; color: #333;">Golf Course Tracker</h3>
      <div id="stats" style="margin-bottom: 15px;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
          <span>Total Courses:</span>
          <span id="total-count">0</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
          <span style="color: #28a745;">Visited:</span>
          <span id="visited-count" style="color: #28a745;">0</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
          <span style="color: #6c757d;">Not Visited:</span>
          <span id="not-visited-count" style="color: #6c757d;">0</span>
        </div>
        <div style="background: #f8f9fa; padding: 8px; border-radius: 4px;">
          <div style="display: flex; justify-content: space-between;">
            <span>Progress:</span>
            <span id="progress-percent">0%</span>
          </div>
          <div style="background: #e9ecef; height: 8px; border-radius: 4px; margin-top: 5px;">
            <div id="progress-bar" style="background: #28a745; height: 100%; border-radius: 4px; width: 0%; transition: width 0.3s ease;"></div>
          </div>
        </div>
      </div>
      <div style="display: flex; gap: 8px; flex-wrap: wrap;">
        <button onclick="exportData()" style="padding: 6px 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">Export</button>
        <button onclick="resetData()" style="padding: 6px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">Reset</button>
        <label for="import-file" style="padding: 6px 12px; background: #28a745; color: white; border-radius: 4px; cursor: pointer; font-size: 12px;">
          Import
          <input type="file" id="import-file" accept=".json" style="display: none;" onchange="importData(this)">
        </label>
      </div>
    </div>
  </div>
  <script src="https://kit.fontawesome.com/91c7a109fb.js" crossorigin="anonymous"></script>
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
    integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
    crossorigin=""></script>
  <script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>
  <script>
    var map = L.map('map').setView([62.160871, 25.6416672], 7);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Golf Data Manager with localStorage
    class GolfDataManager {
      constructor() {
        this.originalKey = 'originalGolfData';
        this.modifiedKey = 'modifiedGolfData';
        this.statusKey = 'golfCourseStatus';
      }

      // Initialize data with status field
      async initializeData() {
        let originalData = localStorage.getItem(this.originalKey);

        if (!originalData) {
          // Load from JSON file for the first time
          const response = await fetch('golf.json');
          const data = await response.json();

          // Add status: false to each item
          const dataWithStatus = data.map(place => ({
            ...place,
            status: false
          }));

          localStorage.setItem(this.originalKey, JSON.stringify(dataWithStatus));
          localStorage.setItem(this.modifiedKey, JSON.stringify(dataWithStatus));
          return dataWithStatus;
        } else {
          // Return modified data if exists, otherwise original
          const modified = localStorage.getItem(this.modifiedKey);
          return JSON.parse(modified || originalData);
        }
      }

      // Update status of a golf course
      updateStatus(courseName, status) {
        const currentData = JSON.parse(localStorage.getItem(this.modifiedKey));
        const courseIndex = currentData.findIndex(place => place.course === courseName);

        if (courseIndex !== -1) {
          currentData[courseIndex].status = status;
          localStorage.setItem(this.modifiedKey, JSON.stringify(currentData));

          // Also store individual status for quick lookup
          const statusData = JSON.parse(localStorage.getItem(this.statusKey) || '{}');
          statusData[courseName] = status;
          localStorage.setItem(this.statusKey, JSON.stringify(statusData));
        }

        return currentData;
      }

      // Get current data
      getCurrentData() {
        const modified = localStorage.getItem(this.modifiedKey);
        const original = localStorage.getItem(this.originalKey);
        return JSON.parse(modified || original || '[]');
      }

      // Get status of a specific course
      getStatus(courseName) {
        const statusData = JSON.parse(localStorage.getItem(this.statusKey) || '{}');
        return statusData[courseName] || false;
      }
    }

    // Initialize golf data manager
    const golfManager = new GolfDataManager();

    // Fetch data from localStorage or JSON file
    async function getPlacesData() {
      return await golfManager.initializeData();
    }

    // Handle checkbox change
    function handleStatusChange(courseName, checkbox) {
      const newStatus = checkbox.checked;
      golfManager.updateStatus(courseName, newStatus);
      console.log(`${courseName} status changed to: ${newStatus}`);

      // Update the label text and color
      const label = checkbox.parentElement;
      const span = label.querySelector('span');
      span.textContent = newStatus ? 'Visited' : 'Not Visited';
      span.style.color = newStatus ? '#28a745' : '#6c757d';

      // Update statistics
      updateStatistics();
    }

    // Update statistics in the control panel
    function updateStatistics() {
      const data = golfManager.getCurrentData();
      const total = data.length;
      const visited = data.filter(place => place.status).length;
      const notVisited = total - visited;
      const progress = total > 0 ? Math.round((visited / total) * 100) : 0;

      document.getElementById('total-count').textContent = total;
      document.getElementById('visited-count').textContent = visited;
      document.getElementById('not-visited-count').textContent = notVisited;
      document.getElementById('progress-percent').textContent = `${progress}%`;
      document.getElementById('progress-bar').style.width = `${progress}%`;
    }

    // Export data as JSON
    function exportData() {
      const data = golfManager.getCurrentData();
      const exportData = {
        exportDate: new Date().toISOString(),
        totalCourses: data.length,
        visitedCourses: data.filter(place => place.status).length,
        data: data
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `golf-courses-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }

    // Reset all data to original state
    function resetData() {
      if (confirm('Are you sure you want to reset all data? This will mark all courses as not visited.')) {
        localStorage.removeItem(golfManager.modifiedKey);
        localStorage.removeItem(golfManager.statusKey);

        // Reload the page to refresh everything
        location.reload();
      }
    }

    // Import data from JSON file
    function importData(input) {
      const file = input.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = function(e) {
        try {
          const importedData = JSON.parse(e.target.result);

          // Validate the imported data
          if (importedData.data && Array.isArray(importedData.data)) {
            localStorage.setItem(golfManager.modifiedKey, JSON.stringify(importedData.data));

            // Update status lookup
            const statusData = {};
            importedData.data.forEach(place => {
              if (place.course && typeof place.status === 'boolean') {
                statusData[place.course] = place.status;
              }
            });
            localStorage.setItem(golfManager.statusKey, JSON.stringify(statusData));

            alert('Data imported successfully!');
            location.reload();
          } else {
            alert('Invalid file format. Please select a valid golf courses JSON file.');
          }
        } catch (error) {
          alert('Error reading file. Please make sure it\'s a valid JSON file.');
        }
      };
      reader.readAsText(file);

      // Clear the input
      input.value = '';
    }

    // Render data to the map
    async function renderGolfPlaces() {
      let placesData = await getPlacesData();
      if (placesData.length === 0) {
        return;
      }

      // Create a marker cluster group
      const markers = L.markerClusterGroup();

      // Loop through the data and add markers to the cluster group
      placesData.forEach(place => {
        const isChecked = place.status ? 'checked' : '';
        const checkboxId = `status-${place.course.replace(/\s+/g, '-').toLowerCase()}`;

        const marker = L.marker([place.lat, place.lng])
          .bindPopup(`
            <div>
              <h3>${place.course}</h3>
              <div style="margin-bottom: 10px;">
                <label style="display: flex; align-items: center; gap: 8px; font-weight: bold;">
                  <input
                    type="checkbox"
                    id="${checkboxId}"
                    ${isChecked}
                    onchange="handleStatusChange('${place.course}', this)"
                    style="transform: scale(1.2);"
                  />
                  <span style="color: ${place.status ? '#28a745' : '#6c757d'};">
                    ${place.status ? 'Visited' : 'Not Visited'}
                  </span>
                </label>
              </div>
              <p>
                <i class="fa-solid fa-house"></i>
                ${place.address}
              </p>
              <p>
                <i class="fa-solid fa-phone"></i>
                ${place.phone}
              </p>
              <p>
                <i class="fa-regular fa-envelope"></i>
                ${place.email}
              </p>
              <p>
                <i class="fa-solid fa-globe"></i>
                <a href="${place.web}" target="_blank">${place.web}</a>
              </p>
              <p>${place.text}</p>
            </div>
          `);

        // Add marker to cluster group
        markers.addLayer(marker);
      });

      // Add the cluster group to the map
      map.addLayer(markers);

      // Update statistics after rendering
      updateStatistics();
    };

    renderGolfPlaces();
  </script>
</body>
</html>
